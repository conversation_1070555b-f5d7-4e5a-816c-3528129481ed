package com.ruoyi.web.controller.hotel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HotelOrder;
import com.ruoyi.system.domain.Conference;
import com.ruoyi.system.domain.Room;
import com.ruoyi.system.service.IHotelOrderService;
import com.ruoyi.system.service.IConferenceService;
import com.ruoyi.system.service.IRoomService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;

/**
 * 酒店订单Controller
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Controller
@RequestMapping("/hotel/order")
public class HotelOrderController extends BaseController
{
    private String prefix = "hotel/order";

    @Autowired
    private IHotelOrderService hotelOrderService;

    @Autowired
    private IConferenceService conferenceService;

    @Autowired
    private IRoomService roomService;

    @RequiresPermissions("hotel:order:view")
    @GetMapping()
    public String order(ModelMap mmap)
    {
        // 获取会议列表用于筛选
        List<Conference> conferenceList = conferenceService.selectConferenceList(new Conference());
        mmap.put("conferenceList", conferenceList);
        return prefix + "/order";
    }

    /**
     * 查询酒店订单列表
     */
    @RequiresPermissions("hotel:order:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(HotelOrder hotelOrder)
    {
        startPage();
        List<HotelOrder> list = hotelOrderService.selectHotelOrderList(hotelOrder);
        return getDataTable(list);
    }

    /**
     * 导出酒店订单列表
     */
    @RequiresPermissions("hotel:order:export")
    @Log(title = "酒店订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(HotelOrder hotelOrder)
    {
        List<HotelOrder> list = hotelOrderService.selectHotelOrderList(hotelOrder);
        ExcelUtil<HotelOrder> util = new ExcelUtil<HotelOrder>(HotelOrder.class);
        return util.exportExcel(list, "酒店订单数据");
    }

    /**
     * 查看订单详情
     */
    @RequiresPermissions("hotel:order:view")
    @GetMapping("/view/{orderId}")
    public String view(@PathVariable("orderId") Long orderId, ModelMap mmap)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        mmap.put("hotelOrder", hotelOrder);
        
        // 获取关联的会议信息
        if (hotelOrder.getConferenceId() != null) {
            Conference conference = conferenceService.selectConferenceById(hotelOrder.getConferenceId());
            mmap.put("conference", conference);
        }
        
        // 获取关联的房型信息
        if (hotelOrder.getRoomId() != null) {
            Room room = roomService.selectRoomById(hotelOrder.getRoomId());
            mmap.put("room", room);
        }
        
        return prefix + "/view";
    }

    /**
     * 修改订单状态页面
     */
    @RequiresPermissions("hotel:order:edit")
    @GetMapping("/edit/{orderId}")
    public String edit(@PathVariable("orderId") Long orderId, ModelMap mmap)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        mmap.put("hotelOrder", hotelOrder);
        
        // 获取关联的会议信息
        if (hotelOrder.getConferenceId() != null) {
            Conference conference = conferenceService.selectConferenceById(hotelOrder.getConferenceId());
            mmap.put("conference", conference);
        }
        
        // 获取关联的房型信息
        if (hotelOrder.getRoomId() != null) {
            Room room = roomService.selectRoomById(hotelOrder.getRoomId());
            mmap.put("room", room);
        }
        
        return prefix + "/edit";
    }

    /**
     * 修改保存订单状态
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(HotelOrder hotelOrder)
    {
        // 只允许修改订单状态和备注
        HotelOrder originalOrder = hotelOrderService.selectHotelOrderByOrderId(hotelOrder.getOrderId());
        if (originalOrder == null) {
            return error("订单不存在");
        }

        // 更新订单状态
        if (!originalOrder.getOrderStatus().equals(hotelOrder.getOrderStatus())) {
            int result = hotelOrderService.updateOrderStatus(originalOrder.getOrderNo(), 
                hotelOrder.getOrderStatus(), getLoginName(), "管理员手动修改");
            if (result <= 0) {
                return error("更新订单状态失败");
            }
        }

        // 更新支付状态
        if (!originalOrder.getPaymentStatus().equals(hotelOrder.getPaymentStatus())) {
            int result = hotelOrderService.updatePaymentStatus(originalOrder.getOrderNo(), 
                hotelOrder.getPaymentStatus(), getLoginName(), "管理员手动修改");
            if (result <= 0) {
                return error("更新支付状态失败");
            }
        }

        // 更新备注
        if (hotelOrder.getRemark() != null && !hotelOrder.getRemark().equals(originalOrder.getRemark())) {
            originalOrder.setRemark(hotelOrder.getRemark());
            originalOrder.setUpdateBy(getLoginName());
            hotelOrderService.updateHotelOrder(originalOrder);
        }

        return success();
    }

    /**
     * 确认订单
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{orderId}")
    @ResponseBody
    public AjaxResult confirm(@PathVariable("orderId") Long orderId)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        if (hotelOrder == null) {
            return error("订单不存在");
        }

        int result = hotelOrderService.confirmOrder(hotelOrder.getOrderNo(), getLoginName());
        return toAjax(result);
    }

    /**
     * 取消订单
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{orderId}")
    @ResponseBody
    public AjaxResult cancel(@PathVariable("orderId") Long orderId, String cancelReason)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        if (hotelOrder == null) {
            return error("订单不存在");
        }

        if (cancelReason == null || cancelReason.trim().isEmpty()) {
            cancelReason = "管理员取消订单";
        }

        int result = hotelOrderService.cancelOrder(hotelOrder.getOrderNo(), cancelReason, getLoginName());
        return toAjax(result);
    }

    /**
     * 退款订单页面
     */
    @RequiresPermissions("hotel:order:edit")
    @GetMapping("/refund/{orderId}")
    public String refund(@PathVariable("orderId") Long orderId, ModelMap mmap)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        if (hotelOrder == null) {
            mmap.put("errorMsg", "订单不存在");
            return "error/404";
        }

        // 验证订单状态
        if (!"PAID".equals(hotelOrder.getOrderStatus()) && !"CONFIRMED".equals(hotelOrder.getOrderStatus())) {
            mmap.put("errorMsg", "只有已支付或已确认的订单才能退款");
            return "error/service";
        }

        mmap.put("hotelOrder", hotelOrder);

        // 获取关联的会议信息
        if (hotelOrder.getConferenceId() != null) {
            Conference conference = conferenceService.selectConferenceById(hotelOrder.getConferenceId());
            mmap.put("conference", conference);
        }

        // 获取关联的房型信息
        if (hotelOrder.getRoomId() != null) {
            Room room = roomService.selectRoomById(hotelOrder.getRoomId());
            mmap.put("room", room);
        }

        return prefix + "/refund";
    }

    /**
     * 退款订单
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/refund/{orderId}")
    @ResponseBody
    public AjaxResult refund(@PathVariable("orderId") Long orderId,
                            java.math.BigDecimal refundAmount,
                            String refundReason)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        if (hotelOrder == null) {
            return error("订单不存在");
        }

        // 验证订单状态
        if (!"PAID".equals(hotelOrder.getOrderStatus()) && !"CONFIRMED".equals(hotelOrder.getOrderStatus())) {
            return error("只有已支付或已确认的订单才能退款");
        }

        // 验证退款金额
        if (refundAmount == null || refundAmount.compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return error("退款金额必须大于0");
        }

        if (refundAmount.compareTo(hotelOrder.getTotalAmount()) > 0) {
            return error("退款金额不能超过订单总金额");
        }

        if (refundReason == null || refundReason.trim().isEmpty()) {
            refundReason = "管理员退款";
        }

        Pair<Integer, String> result = hotelOrderService.refundOrder(hotelOrder.getOrderNo(), refundAmount, refundReason, getLoginName());
        return result.getLeft() > 0 ? success() : error(result.getRight());
    }

    /**
     * 批量退款订单页面
     */
    @RequiresPermissions("hotel:order:edit")
    @GetMapping("/batchRefund")
    public String batchRefund(String orderIds, ModelMap mmap)
    {
        if (StringUtils.isEmpty(orderIds)) {
            mmap.put("errorMsg", "请选择要退款的订单");
            return "error/404";
        }

        // 解析订单ID数组
        Long[] orderIdArray = Convert.toLongArray(orderIds);
        List<HotelOrder> orders = new ArrayList<>();

        for (Long orderId : orderIdArray) {
            HotelOrder order = hotelOrderService.selectHotelOrderByOrderId(orderId);
            if (order != null) {
                // 验证订单状态
                if ("PAID".equals(order.getOrderStatus()) || "CONFIRMED".equals(order.getOrderStatus())) {
                    orders.add(order);
                }
            }
        }

        if (orders.isEmpty()) {
            mmap.put("errorMsg", "没有可退款的订单");
            return "error/service";
        }

        mmap.put("orders", orders);
        mmap.put("orderIds", orderIds);
        return prefix + "/batchRefund";
    }

    /**
     * 批量退款订单
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchRefund")
    @ResponseBody
    public AjaxResult batchRefund(String orderIds, String refundReason)
    {
        if (StringUtils.isEmpty(orderIds)) {
            return error("请选择要退款的订单");
        }

        if (StringUtils.isEmpty(refundReason)) {
            refundReason = "管理员批量退款";
        }

        // 解析订单ID数组
        Long[] orderIdArray = Convert.toLongArray(orderIds);

        // 调用批量退款服务
        Map<String, Object> result = hotelOrderService.batchRefundOrders(orderIdArray, refundReason, getLoginName());

        int successCount = (Integer) result.get("successCount");
        int failCount = (Integer) result.get("failCount");
        @SuppressWarnings("unchecked")
        List<String> failMessages = (List<String>) result.get("failMessages");

        if (successCount > 0 && failCount == 0) {
            return success("批量退款成功，共处理 " + successCount + " 个订单");
        } else if (successCount > 0 && failCount > 0) {
            String message = String.format("批量退款部分成功，成功 %d 个，失败 %d 个。失败原因：%s",
                successCount, failCount, String.join("；", failMessages));
            return AjaxResult.warn(message);
        } else {
            String message = "批量退款失败，失败原因：" + String.join("；", failMessages);
            return error(message);
        }
    }

    /**
     * 删除酒店订单
     */
    @RequiresPermissions("hotel:order:remove")
    @Log(title = "酒店订单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(hotelOrderService.deleteHotelOrderByOrderIds(Convert.toLongArray(ids)));
    }

    /**
     * 获取订单统计信息
     */
    @RequiresPermissions("hotel:order:list")
    @PostMapping("/statistics")
    @ResponseBody
    public AjaxResult statistics()
    {
        // 统计各状态订单数量
        HotelOrder queryOrder = new HotelOrder();
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.PENDING);
        int pendingCount = hotelOrderService.countHotelOrders(queryOrder);
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.PAID);
        int paidCount = hotelOrderService.countHotelOrders(queryOrder);
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.CONFIRMED);
        int confirmedCount = hotelOrderService.countHotelOrders(queryOrder);
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.CANCELLED);
        int cancelledCount = hotelOrderService.countHotelOrders(queryOrder);
        
        queryOrder.setOrderStatus(HotelOrder.OrderStatus.REFUNDED);
        int refundedCount = hotelOrderService.countHotelOrders(queryOrder);

        // 构建统计结果
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("pendingCount", pendingCount);
        statistics.put("paidCount", paidCount);
        statistics.put("confirmedCount", confirmedCount);
        statistics.put("cancelledCount", cancelledCount);
        statistics.put("refundedCount", refundedCount);
        statistics.put("totalCount", pendingCount + paidCount + confirmedCount + cancelledCount + refundedCount);

        return success(statistics);
    }

    /**
     * 刷新订单状态
     */
    @RequiresPermissions("hotel:order:edit")
    @Log(title = "酒店订单", businessType = BusinessType.UPDATE)
    @PostMapping("/refresh/{orderId}")
    @ResponseBody
    public AjaxResult refreshOrderStatus(@PathVariable("orderId") Long orderId)
    {
        HotelOrder hotelOrder = hotelOrderService.selectHotelOrderByOrderId(orderId);
        if (hotelOrder == null) {
            return error("订单不存在");
        }

        try {
            int result = hotelOrderService.refreshOrderStatus(hotelOrder.getOrderNo(), getLoginName());
            if (result > 0) {
                return success("订单状态刷新成功");
            } else {
                return error("订单状态刷新失败");
            }
        } catch (Exception e) {
            return error("刷新订单状态异常：" + e.getMessage());
        }
    }
}
