<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('批量退款订单')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-batch-refund">
            <input name="orderIds" th:value="${orderIds}" type="hidden">
            
            <div class="form-group">
                <label class="col-sm-3 control-label">选中订单：</label>
                <div class="col-sm-8">
                    <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>房间名称</th>
                                    <th>入住日期</th>
                                    <th>退房日期</th>
                                    <th>订单金额</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="order : ${orders}">
                                    <td th:text="${order.orderNo}"></td>
                                    <td th:text="${order.roomName}"></td>
                                    <td th:text="${#dates.format(order.checkinDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(order.checkoutDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="'¥' + ${order.totalAmount}"></td>
                                    <td>
                                        <span th:if="${order.orderStatus == 'PAID'}" class="label label-success">已支付</span>
                                        <span th:if="${order.orderStatus == 'CONFIRMED'}" class="label label-info">已确认</span>
                                        <span th:if="${order.orderStatus == 'PENDING'}" class="label label-warning">待支付</span>
                                        <span th:if="${order.orderStatus == 'CANCELLED'}" class="label label-default">已取消</span>
                                        <span th:if="${order.orderStatus == 'REFUNDED'}" class="label label-danger">已退款</span>
                                        <span th:if="${order.orderStatus == 'COMPLETED'}" class="label label-primary">已完成</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label">总退款金额：</label>
                <div class="col-sm-8">
                    <p class="form-control-static text-danger" style="font-size: 16px; font-weight: bold;">
                        ¥<span th:text="${#aggregates.sum(orders.![totalAmount])}"></span>
                    </p>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">退款原因：</label>
                <div class="col-sm-8">
                    <textarea name="refundReason" class="form-control" rows="4" placeholder="请输入退款原因（至少5个字符）" required></textarea>
                </div>
            </div>
            
            <div class="form-group">
                <div class="col-sm-8 col-sm-offset-3">
                    <div class="alert alert-warning">
                        <i class="fa fa-warning"></i>
                        <strong>注意：</strong>
                        <ul style="margin: 10px 0 0 0; padding-left: 20px;">
                            <li>批量退款将对所有选中订单执行全额退款</li>
                            <li>退款操作不可撤销，请谨慎操作</li>
                            <li>退款将通过微信支付原路退回</li>
                            <li>退款到账时间通常为1-3个工作日</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-8 col-sm-offset-3">
                    <button class="btn btn-danger" type="submit">
                        <i class="fa fa-undo"></i> 确认批量退款
                    </button>
                    <button onclick="$.modal.closeHandler()" class="btn btn-default" type="button">取消</button>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "hotel/order";
        $("#form-batch-refund").validate({
            focusCleanup: true,
            rules: {
                refundReason: {
                    required: true,
                    minlength: 5
                }
            },
            messages: {
                refundReason: {
                    required: "请输入退款原因",
                    minlength: "退款原因至少需要5个字符"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                var orderIds = $("input[name='orderIds']").val();
                var refundReason = $("textarea[name='refundReason']").val();
                
                $.modal.confirm("确认要批量退款这些订单吗？此操作不可撤销！", function() {
                    $.modal.loading("正在处理批量退款，请稍候...");
                    
                    $.post(prefix + "/batchRefund", {
                        orderIds: orderIds,
                        refundReason: refundReason
                    }, function(result) {
                        $.modal.closeLoading();
                        if (result.code == 0) {
                            $.modal.msgSuccess(result.msg);
                            $.modal.closeHandler();
                            if (parent.$.table) {
                                parent.$.table.refresh();
                            }
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }).fail(function(xhr, status, error) {
                        $.modal.closeLoading();
                        $.modal.alertError("批量退款请求失败: " + xhr.statusText);
                    });
                });
            }
        }

        $("form").submit(function(e) {
            e.preventDefault();
            submitHandler();
        });
    </script>
</body>
</html>
