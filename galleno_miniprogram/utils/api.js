// api.js - 网络请求工具
const util = require('./util.js')

// API配置
const API_CONFIG = {
  // 开发环境
  dev: {
    baseUrl: 'http://192.168.31.120/api',
    timeout: 10000
  },
  // 生产环境
  prod: {
    baseUrl: 'https://galleno.potatocat.cn/api',
    timeout: 10000
  }
}

// 当前环境配置
const ENV = 'prod' // 可以根据需要切换环境
const config = API_CONFIG[ENV]

/**
 * 网络请求封装
 * @param {Object} options 请求参数
 * @param {string} options.url 请求地址
 * @param {string} options.method 请求方法
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 * @param {boolean} options.showLoading 是否显示加载中
 * @param {boolean} options.showError 是否显示错误提示
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 显示加载中
    if (options.showLoading !== false) {
      util.showLoading(options.loadingText || '请求中...')
    }

    // 获取token
    const token = wx.getStorageSync('token')
    
    // 构建请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    }
    
    // 添加token到请求头
    if (token) {
      header['Authorization'] = `Bearer ${token}`
    }

    wx.request({
      url: config.baseUrl + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: header,
      timeout: config.timeout,
      success: (res) => {
        console.log('API请求成功:', options.url, res)
        
        // 隐藏加载中
        if (options.showLoading !== false) {
          util.hideLoading()
        }

        // 处理响应
        if (res.statusCode === 200) {
          const data = res.data
          
          // 根据后端AjaxResult格式处理
          if (data.code === 0) {
            // 成功
            resolve(data.data || data)
          } else if (data.code === 301) {
            // 警告
            if (options.showError !== false) {
              util.showMessage(data.msg || '请求警告', 'none')
            }
            resolve(data.data || data)
          } else {
            // 错误
            if (options.showError !== false) {
              util.showMessage(data.msg || '请求失败', 'none')
            }
            reject(new Error(data.msg || '请求失败'))
          }
        } else if (res.statusCode === 401) {
          // 未授权，清除token并跳转登录
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')
          
          if (options.showError !== false) {
            util.showMessage('登录已过期，请重新登录', 'none')
          }
          
          reject(new Error('未授权'))
        } else {
          // 其他错误
          if (options.showError !== false) {
            util.showMessage(`请求失败(${res.statusCode})`, 'none')
          }
          reject(new Error(`请求失败(${res.statusCode})`))
        }
      },
      fail: (err) => {
        console.error('API请求失败:', options.url, err)
        
        // 隐藏加载中
        if (options.showLoading !== false) {
          util.hideLoading()
        }

        // 显示错误提示
        if (options.showError !== false) {
          if (err.errMsg && err.errMsg.includes('timeout')) {
            util.showMessage('请求超时，请检查网络', 'none')
          } else if (err.errMsg && err.errMsg.includes('fail')) {
            util.showMessage('网络连接失败', 'none')
          } else {
            util.showMessage('网络异常', 'none')
          }
        }

        reject(err)
      }
    })
  })
}

/**
 * GET请求
 */
const get = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

/**
 * POST请求
 */
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 */
const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 */
const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

// API接口定义
const API = {
  // 用户相关
  user: {
    // 小程序登录
    login: (jsCode) => post('/wechat/login', { jsCode }),

    // 获取用户信息
    getUserInfo: () => get('/hotel/user/info'),

    // 更新用户信息
    updateUserInfo: (userInfo) => put('/user/info', userInfo)
  },

  // 会议相关
  conference: {
    // 获取启用的会议列表（小程序首页）
    getConferences: () => get('/hotel/conferences'),

    // 获取会议详情
    getConference: (id) => get('/hotel/conference', { id }),

    // 验证会议识别码
    validateCode: (categoryId, conferenceId) => post('/hotel/validateCode', { categoryId, conferenceId }),

    // 根据识别码获取房型信息
    getRoomByCode: (categoryId, conferenceId) => post('/hotel/getRoomByCode', { categoryId, conferenceId })
  },

  // 酒店相关
  hotel: {
    // 根据会议ID获取房型列表
    getRooms: (conferenceId) => get('/hotel/rooms', { conferenceId }),

    // 获取房型详情
    getRoom: (id) => get('/hotel/room', { id })
  },

  // 订单相关
  order: {
    // 创建订单并发起支付
    create: (orderData) => post('/hotel/order/create', orderData),

    // 获取订单列表
    getList: (status) => get('/hotel/order/list', { status }),

    // 获取订单详情
    getDetail: (orderId) => get(`/hotel/order/${orderId}`),

    // 取消订单
    cancel: (orderId) => put(`/hotel/order/${orderId}/cancel`),

    // 获取支付参数
    getPaymentParams: (orderNo) => get('/hotel/order/payment-params', { orderNo })
  },

  // 支付相关
  payment: {
    // 查询并刷新支付订单状态（默认刷新）
    queryAndRefreshPayment: (orderNo) => get(`/hotel/payment/query`, { orderNo })
  }
}

module.exports = {
  request,
  get,
  post,
  put,
  del,
  API,
  config
}
