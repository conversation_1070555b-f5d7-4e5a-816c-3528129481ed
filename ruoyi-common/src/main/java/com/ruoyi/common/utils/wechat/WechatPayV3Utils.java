package com.ruoyi.common.utils.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.ruoyi.common.config.WechatMiniAppConfig;
import com.ruoyi.common.config.WechatPayConfig;
import com.ruoyi.common.constant.WechatConstants;
import com.ruoyi.common.core.domain.WechatPayParams;
import com.ruoyi.common.core.domain.WechatPayV3Result;
import com.ruoyi.common.core.domain.WechatRefundResult;
import com.ruoyi.common.core.domain.WechatPayV3NotifyData;
import com.ruoyi.common.core.domain.request.wechat.WechatPayRequest;
import com.ruoyi.common.core.domain.request.wechat.WechatPayV3Request;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.http.HttpResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.io.ByteArrayInputStream;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * 微信支付V3版本工具类
 *
 * <AUTHOR>
 */
@Component
public class WechatPayV3Utils {
    private static final Logger log = LoggerFactory.getLogger(WechatPayV3Utils.class);

    @Autowired
    private WechatMiniAppConfig miniAppConfig;

    @Autowired
    private WechatPayConfig payConfig;

    private PrivateKey merchantPrivateKey;

    /** 微信平台公钥缓存 */
    private static final Map<String, PublicKey> PLATFORM_PUBLIC_KEY_CACHE = new ConcurrentHashMap<>();

    /** 微信平台公钥 */
    private PublicKey wechatPlatformPublicKey;

    /**
     * 小程序下单
     *
     * @param payRequest 支付请求参数
     * @return 支付结果
     */
    public WechatPayV3Result jsapiPay(WechatPayRequest payRequest) {
        try {
            // 构建V3请求参数
            WechatPayV3Request v3Request = buildJsapiPayRequest(payRequest);

            // 转换为JSON
            // 配置序列化策略：驼峰转下划线
            SerializeConfig config = new SerializeConfig();
            config.setPropertyNamingStrategy(PropertyNamingStrategy.SnakeCase);
            String requestBody = JSON.toJSONString(v3Request, config);
            log.debug("微信支付V3下单请求: {}", requestBody);

            // 构建HTTP请求头
            String url = WechatConstants.WECHAT_PAY_V3_BASE_URL + WechatConstants.WECHAT_PAY_V3_JSAPI_URL;
            Map<String, String> headers = buildHttpHeaders("POST", url, requestBody);

            // 发送请求
            HttpResponse response = HttpUtils.sendSSLPostWithResponse(url, requestBody,
                WechatConstants.WECHAT_PAY_V3_CONTENT_TYPE, headers);

            log.debug("微信支付V3下单响应状态: {}", response.getStatusCode());
            log.debug("微信支付V3下单响应: {}", response.getBody());

            if (response.isSuccess()) {
                WechatPayV3Result result = JSON.parseObject(response.getBody(), WechatPayV3Result.class);
                log.info("微信支付V3下单成功，订单号: {}, prepay_id: {}", payRequest.getOutTradeNo(), result.getPrepayId());
                return result;
            } else {
                log.error("微信支付V3下单失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                return createErrorResult("微信支付下单失败：" + response.getBody());
            }
        } catch (Exception e) {
            log.error("微信支付V3下单异常", e);
            return createErrorResult("系统异常：" + e.getMessage());
        }
    }

    /**
     * 生成小程序支付参数
     *
     * @param prepayId 预支付交易会话标识
     * @return 小程序支付参数
     */
    public WechatPayParams generateMiniAppPayParams(String prepayId) {
        try {
            String appId = miniAppConfig.getAppId();
            String timeStamp = String.valueOf(Instant.now().getEpochSecond());
            String nonceStr = generateNonceStr();
            String packageValue = "prepay_id=" + prepayId;
            String signType = "RSA";

            // 构建签名字符串
            String signString = appId + "\n" + timeStamp + "\n" + nonceStr + "\n" + packageValue + "\n";
            
            // 生成签名
            String paySign = signWithRSA(signString);

            WechatPayParams payParams = new WechatPayParams(appId, timeStamp, nonceStr, packageValue, signType, paySign);
            log.info("生成小程序支付参数成功，prepay_id: {}", prepayId);
            
            return payParams;
        } catch (Exception e) {
            log.error("生成小程序支付参数异常", e);
            return null;
        }
    }

    /**
     * 查询订单
     *
     * @param outTradeNo 商户订单号
     * @return 支付结果
     */
    public WechatPayV3Result queryOrder(String outTradeNo) {
        try {
            String url = WechatConstants.WECHAT_PAY_V3_BASE_URL +
                String.format(WechatConstants.WECHAT_PAY_V3_QUERY_BY_OUT_TRADE_NO_URL, outTradeNo);
            String queryParam = "mchid=" + payConfig.getMchId();

            // 构建HTTP请求头
            Map<String, String> headers = buildHttpHeaders("GET", url + "?" + queryParam, "");

            // 发送请求
            HttpResponse response = HttpUtils.sendSSLGetWithResponse(url, queryParam, headers);

            log.debug("微信支付V3查询订单响应状态: {}", response.getStatusCode());
            log.debug("微信支付V3查询订单响应: {}", response.getBody());

            if (response.isSuccess()) {
                WechatPayV3Result result = JSON.parseObject(response.getBody(), WechatPayV3Result.class);
                log.info("微信支付V3查询订单成功，订单号: {}, 交易状态: {}", outTradeNo, result.getTradeState());
                return result;
            } else {
                log.error("微信支付V3查询订单失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                return createErrorResult("查询订单失败：" + response.getBody());
            }
        } catch (Exception e) {
            log.error("微信支付V3查询订单异常", e);
            return createErrorResult("系统异常：" + e.getMessage());
        }
    }

    /**
     * 构建小程序支付请求参数
     */
    private WechatPayV3Request buildJsapiPayRequest(WechatPayRequest payRequest) {
        WechatPayV3Request v3Request = new WechatPayV3Request();
        v3Request.setAppid(miniAppConfig.getAppId());
        v3Request.setMchid(payConfig.getMchId());
        v3Request.setDescription(payRequest.getBody());
        v3Request.setOutTradeNo(payRequest.getOutTradeNo());
        v3Request.setNotifyUrl(payConfig.getNotifyUrl());
        v3Request.setAttach(payRequest.getAttach());

        // 设置金额信息
        WechatPayV3Request.Amount amount = new WechatPayV3Request.Amount();
        amount.setTotal(payRequest.getTotalFee());
        amount.setCurrency("CNY");
        v3Request.setAmount(amount);

        // 设置支付者信息
        WechatPayV3Request.Payer payer = new WechatPayV3Request.Payer();
        payer.setOpenid(payRequest.getOpenid());
        v3Request.setPayer(payer);

        // 设置场景信息
        WechatPayV3Request.SceneInfo sceneInfo = new WechatPayV3Request.SceneInfo();
        sceneInfo.setPayerClientIp(StringUtils.isNotEmpty(payRequest.getSpbillCreateIp()) ? 
            payRequest.getSpbillCreateIp() : "127.0.0.1");
        v3Request.setSceneInfo(sceneInfo);

        // 设置过期时间
        if (StringUtils.isNotEmpty(payRequest.getTimeExpire())) {
            v3Request.setTimeExpire(payRequest.getTimeExpire());
        } else {
            // 默认30分钟后过期
            String expireTime = Instant.now().plusSeconds(payConfig.getTimeoutMinutes() * 60)
                .atZone(ZoneId.of("Asia/Shanghai"))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+08:00'"));
            v3Request.setTimeExpire(expireTime);
        }

        return v3Request;
    }

    /**
     * 根据微信交易号查询订单
     *
     * @param transactionId 微信交易号
     * @return 支付结果
     */
    public WechatPayV3Result queryOrderByTransactionId(String transactionId) {
        try {
            String url = WechatConstants.WECHAT_PAY_V3_BASE_URL +
                String.format(WechatConstants.WECHAT_PAY_V3_QUERY_BY_TRANSACTION_ID_URL, transactionId);
            String queryParam = "mchid=" + payConfig.getMchId();

            // 构建HTTP请求头
            Map<String, String> headers = buildHttpHeaders("GET", url + "?" + queryParam, "");

            // 发送请求
            HttpResponse response = HttpUtils.sendSSLGetWithResponse(url, queryParam, headers);

            log.debug("微信支付V3查询订单响应状态: {}", response.getStatusCode());
            log.debug("微信支付V3查询订单响应: {}", response.getBody());

            if (response.isSuccess()) {
                WechatPayV3Result result = JSON.parseObject(response.getBody(), WechatPayV3Result.class);
                log.info("微信支付V3查询订单成功，微信交易号: {}", transactionId);
                return result;
            } else {
                log.error("微信支付V3查询订单失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                return createErrorResult("微信支付查询订单失败：" + response.getBody());
            }
        } catch (Exception e) {
            log.error("微信支付V3查询订单异常", e);
            return createErrorResult("系统异常：" + e.getMessage());
        }
    }

    /**
     * 申请退款
     *
     * @param outTradeNo 商户订单号
     * @param outRefundNo 商户退款单号
     * @param refundAmount 退款金额（分）
     * @param totalAmount 原订单金额（分）
     * @param refundReason 退款原因
     * @return 退款结果
     */
    public WechatRefundResult refundOrder(String outTradeNo, String outRefundNo,
                                         Long refundAmount, Long totalAmount, String refundReason) {
        try {
            // 构建退款请求参数
            Map<String, Object> refundRequest = new HashMap<>();
            refundRequest.put("out_trade_no", outTradeNo);
            refundRequest.put("out_refund_no", outRefundNo);
            refundRequest.put("reason", refundReason);
            refundRequest.put("notify_url", payConfig.getNotifyUrl().replace("/pay/notify", "/refund/notify"));

            // 金额信息
            Map<String, Object> amount = new HashMap<>();
            amount.put("refund", refundAmount);
            amount.put("total", totalAmount);
            amount.put("currency", "CNY");
            refundRequest.put("amount", amount);

            String requestBody = JSON.toJSONString(refundRequest);
            log.debug("微信支付V3退款请求: {}", requestBody);

            // 构建HTTP请求头
            String url = WechatConstants.WECHAT_PAY_V3_BASE_URL + WechatConstants.WECHAT_PAY_V3_REFUND_URL;
            Map<String, String> headers = buildHttpHeaders("POST", url, requestBody);

            // 发送请求
            HttpResponse response = HttpUtils.sendSSLPostWithResponse(url, requestBody,
                WechatConstants.WECHAT_PAY_V3_CONTENT_TYPE, headers);

            log.debug("微信支付V3退款响应状态: {}", response.getStatusCode());
            log.debug("微信支付V3退款响应: {}", response.getBody());

            if (response.isSuccess()) {
                WechatRefundResult result = JSON.parseObject(response.getBody(), WechatRefundResult.class);
                log.info("微信支付V3退款申请成功，订单号: {}, 退款单号: {}, 退款状态: {}",
                    outTradeNo, outRefundNo, result.getStatus());
                return result;
            } else {
                log.error("微信支付V3退款申请失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                return createRefundErrorResult("微信支付退款失败", JSONObject.parseObject(response.getBody()));
            }
        } catch (Exception e) {
            log.error("微信支付V3退款申请异常", e);
            return createRefundErrorResult("系统异常", new JSONObject());
        }
    }

    /**
     * 查询退款状态
     *
     * @param outRefundNo 商户退款单号
     * @return 退款查询结果
     */
    public WechatRefundResult queryRefund(String outRefundNo) {
        try {
            String url = WechatConstants.WECHAT_PAY_V3_BASE_URL +
                String.format(WechatConstants.WECHAT_PAY_V3_REFUND_QUERY_URL, outRefundNo);

            // 构建HTTP请求头
            Map<String, String> headers = buildHttpHeaders("GET", url, "");

            // 发送请求
            HttpResponse response = HttpUtils.sendSSLGetWithResponse(url, "", headers);

            log.debug("微信支付V3退款查询响应状态: {}", response.getStatusCode());
            log.debug("微信支付V3退款查询响应: {}", response.getBody());

            if (response.isSuccess()) {
                WechatRefundResult result = JSON.parseObject(response.getBody(), WechatRefundResult.class);
                log.info("微信支付V3退款查询成功，退款单号: {}, 退款状态: {}", outRefundNo, result.getStatus());
                return result;
            } else {
                log.error("微信支付V3退款查询失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                return createRefundErrorResult("微信支付退款查询失败", JSONObject.parseObject(response.getBody()));
            }
        } catch (Exception e) {
            log.error("微信支付V3退款查询异常", e);
            return createRefundErrorResult("系统异常", new JSONObject());
        }
    }

    /**
     * 构建HTTP请求头
     */
    private Map<String, String> buildHttpHeaders(String method, String url, String body) throws Exception {
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        String nonceStr = generateNonceStr();

        // 构建签名字符串
        String path = url.substring(WechatConstants.WECHAT_PAY_V3_BASE_URL.length());
        String signString = method + "\n" + path + "\n" + timestamp + "\n" + nonceStr + "\n" + body + "\n";

        // 生成签名
        String signature = signWithRSA(signString);

        // 构建Authorization头
        String authorization = String.format("WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%s\",serial_no=\"%s\",signature=\"%s\"",
            payConfig.getMchId(), nonceStr, timestamp, payConfig.getMerchantSerialNumber(), signature);

        // 构建请求头Map
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", WechatConstants.WECHAT_PAY_V3_ACCEPT);
        headers.put("User-Agent", WechatConstants.WECHAT_PAY_V3_USER_AGENT);
        headers.put("Authorization", authorization);

        return headers;
    }

    /**
     * 创建退款错误结果
     */
    private WechatRefundResult createRefundErrorResult(String errorMessage, JSONObject refundResult) {

        WechatRefundResult errorResult = new WechatRefundResult();
        String code = refundResult.getString("code");
        errorResult.setStatus(code != null ? code : "ERROR");
        String message = refundResult.getString("message");
        errorResult.setMessage(message != null ? message : errorMessage);
        return errorResult;
    }

    /**
     * RSA签名
     */
    private String signWithRSA(String data) throws Exception {
        if (merchantPrivateKey == null) {
            merchantPrivateKey = loadPrivateKey();
        }

        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(merchantPrivateKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        
        return Base64.getEncoder().encodeToString(signature.sign());
    }

    /**
     * 加载商户私钥
     */
    private PrivateKey loadPrivateKey() throws Exception {
        String privateKeyPath = payConfig.getPrivateKeyPath();
        if (StringUtils.isEmpty(privateKeyPath)) {
            throw new IllegalArgumentException("商户私钥文件路径未配置");
        }

        String privateKeyContent = new String(Files.readAllBytes(Paths.get(privateKeyPath)), StandardCharsets.UTF_8)
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 验证微信支付V3版本回调通知签名
     *
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 回调通知的请求体
     * @param signature 微信签名
     * @param serialNumber 微信平台证书序列号（可选，用于日志记录）
     * @return 验签是否成功
     */
    public boolean verifyNotifySignature(String timestamp, String nonce, String body, String signature, String serialNumber) {
        try {
            log.info("开始微信支付V3回调验签，证书序列号: {}", serialNumber);

            // 开发环境跳过验签
            if (isSkipSignatureVerification()) {
                log.warn("开发环境跳过微信支付V3回调验签");
                return true;
            }

            // 构建验签字符串
            String signString = timestamp + "\n" + nonce + "\n" + body + "\n";
            log.debug("微信支付V3回调验签字符串: {}", signString);

            // 获取微信平台公钥
            PublicKey wechatPublicKey = getWechatPlatformPublicKey();
            if (wechatPublicKey == null) {
                log.error("无法获取微信平台公钥");
                return false;
            }

            // 验证签名
            Signature verifySignature = Signature.getInstance("SHA256withRSA");
            verifySignature.initVerify(wechatPublicKey);
            verifySignature.update(signString.getBytes(StandardCharsets.UTF_8));

            boolean isValid = verifySignature.verify(Base64.getDecoder().decode(signature));
            log.info("微信支付V3回调验签结果: {}", isValid ? "成功" : "失败");

            return isValid;
        } catch (Exception e) {
            log.error("微信支付V3回调验签异常", e);
            return false;
        }
    }

    /**
     * 获取微信平台公钥
     * 支持从配置文件或直接配置的公钥字符串中加载
     *
     * @return 微信平台公钥
     */
    private PublicKey getWechatPlatformPublicKey() {
        try {
            // 如果已经加载过，直接返回
            if (wechatPlatformPublicKey != null) {
                return wechatPlatformPublicKey;
            }

            // 开发环境跳过验签
            if (isSkipSignatureVerification()) {
                log.warn("开发环境跳过微信支付V3回调验签");
                return createDummyPublicKey();
            }

            // 尝试从配置中加载公钥
            String publicKeyString = payConfig.getWechatPlatformPublicKey();
            if (StringUtils.isNotEmpty(publicKeyString)) {
                wechatPlatformPublicKey = loadPublicKeyFromString(publicKeyString);
                log.info("从配置中加载微信平台公钥成功");
                return wechatPlatformPublicKey;
            }

            // 尝试从文件中加载公钥
            String publicKeyPath = payConfig.getWechatPlatformPublicKeyPath();
            if (StringUtils.isNotEmpty(publicKeyPath)) {
                wechatPlatformPublicKey = loadPublicKeyFromFile(publicKeyPath);
                log.info("从文件中加载微信平台公钥成功: {}", publicKeyPath);
                return wechatPlatformPublicKey;
            }

            log.error("未配置微信平台公钥，请在配置文件中设置 wechatPlatformPublicKey 或 wechatPlatformPublicKeyPath");
            return null;
        } catch (Exception e) {
            log.error("获取微信平台公钥异常", e);
            return null;
        }
    }

    /**
     * 检查是否跳过签名验证（仅用于开发测试）
     */
    private boolean isSkipSignatureVerification() {
        // 可以通过配置文件或环境变量控制
        // 这里简单检查是否为开发环境
        String profile = System.getProperty("spring.profiles.active", "");
        return "dev".equals(profile) || "test".equals(profile);
    }

    /**
     * 创建虚拟公钥（仅用于开发测试时跳过验签）
     */
    private PublicKey createDummyPublicKey() {
        try {
            // 生成一个临时的RSA密钥对用于测试
            java.security.KeyPairGenerator keyGen = java.security.KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(2048);
            java.security.KeyPair keyPair = keyGen.generateKeyPair();
            return keyPair.getPublic();
        } catch (Exception e) {
            log.error("创建虚拟公钥失败", e);
            return null;
        }
    }

    /**
     * 从字符串加载公钥
     *
     * @param publicKeyString 公钥字符串（PEM格式）
     * @return 公钥
     */
    private PublicKey loadPublicKeyFromString(String publicKeyString) throws Exception {
        // 移除公钥头尾标识和空白字符
        String cleanKey = publicKeyString
            .replace("-----BEGIN PUBLIC KEY-----", "")
            .replace("-----END PUBLIC KEY-----", "")
            .replaceAll("\\s", "");

        // 解码公钥
        byte[] keyBytes = Base64.getDecoder().decode(cleanKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");

        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 从文件加载公钥
     *
     * @param publicKeyPath 公钥文件路径
     * @return 公钥
     */
    private PublicKey loadPublicKeyFromFile(String publicKeyPath) throws Exception {
        String publicKeyContent = new String(Files.readAllBytes(Paths.get(publicKeyPath)), StandardCharsets.UTF_8);
        return loadPublicKeyFromString(publicKeyContent);
    }

    /**
     * 设置微信平台公钥（用于动态配置）
     *
     * @param publicKeyString 公钥字符串（PEM格式）
     */
    public void setWechatPlatformPublicKey(String publicKeyString) {
        try {
            this.wechatPlatformPublicKey = loadPublicKeyFromString(publicKeyString);
            log.info("动态设置微信平台公钥成功");
        } catch (Exception e) {
            log.error("动态设置微信平台公钥失败", e);
        }
    }

    /**
     * 重新加载微信平台公钥
     */
    public void reloadWechatPlatformPublicKey() {
        this.wechatPlatformPublicKey = null;
        log.info("重新加载微信平台公钥");
    }

    /**
     * 生成随机字符串
     */
    private String generateNonceStr() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }

    /**
     * 创建错误结果
     */
    private WechatPayV3Result createErrorResult(String errorMsg) {
        WechatPayV3Result result = new WechatPayV3Result();
        result.setTradeState("FAIL");
        result.setTradeStateDesc(errorMsg);
        return result;
    }

    /**
     * 解密微信支付V3回调通知中的resource字段
     *
     * @param resource 加密的resource数据
     * @return 解密后的业务数据JSON字符串
     */
    public String decryptNotifyResource(WechatPayV3NotifyData.Resource resource) {
        try {
            log.info("开始解密微信支付V3回调通知resource字段");

            if (resource == null) {
                log.error("resource数据为空");
                return null;
            }

            // 验证加密算法
            if (!"AEAD_AES_256_GCM".equals(resource.getAlgorithm())) {
                log.error("不支持的加密算法: {}", resource.getAlgorithm());
                return null;
            }

            // 获取解密参数
            String ciphertext = resource.getCiphertext();
            String nonce = resource.getNonce();
            String associatedData = resource.getAssociatedData();

            if (StringUtils.isEmpty(ciphertext) || StringUtils.isEmpty(nonce)) {
                log.error("解密参数不完整，ciphertext: {}, nonce: {}",
                    StringUtils.isEmpty(ciphertext) ? "空" : "有值",
                    StringUtils.isEmpty(nonce) ? "空" : "有值");
                return null;
            }

            // 获取APIv3密钥
            String apiV3Key = payConfig.getApiV3Key();
            if (StringUtils.isEmpty(apiV3Key)) {
                log.error("APIv3密钥未配置");
                return null;
            }

            // 执行AES-GCM解密
            String decryptedData = decryptAesGcm(ciphertext, apiV3Key, nonce, associatedData);

            if (decryptedData != null) {
                log.info("微信支付V3回调通知resource字段解密成功");
                log.debug("解密后的数据: {}", decryptedData);
            } else {
                log.error("微信支付V3回调通知resource字段解密失败");
            }

            return decryptedData;
        } catch (Exception e) {
            log.error("解密微信支付V3回调通知resource字段异常", e);
            return null;
        }
    }

    /**
     * AES-GCM解密
     *
     * @param ciphertext 密文（Base64编码）
     * @param key 密钥
     * @param nonce 随机数
     * @param associatedData 附加数据
     * @return 解密后的明文
     */
    private String decryptAesGcm(String ciphertext, String key, String nonce, String associatedData) {
        try {
            // Base64解码密文
            byte[] ciphertextBytes = Base64.getDecoder().decode(ciphertext);
            byte[] nonceBytes = nonce.getBytes(StandardCharsets.UTF_8);
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);

            // 创建密钥规范
            SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, "AES");

            // 创建GCM参数规范（GCM标签长度为128位）
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, nonceBytes);

            // 创建Cipher实例
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);

            // 设置附加数据（如果存在）
            if (StringUtils.isNotEmpty(associatedData)) {
                cipher.updateAAD(associatedData.getBytes(StandardCharsets.UTF_8));
            }

            // 执行解密
            byte[] decryptedBytes = cipher.doFinal(ciphertextBytes);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES-GCM解密失败", e);
            return null;
        }
    }
}
