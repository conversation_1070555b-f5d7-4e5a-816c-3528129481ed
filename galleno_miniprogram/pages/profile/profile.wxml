<!--profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <!-- 未登录状态 -->
    <view class="login-prompt" wx:if="{{!isLoggedIn}}">
      <view class="login-avatar">
        <text class="avatar-icon">👤</text>
      </view>
      <text class="login-title">登录享受更多服务</text>
      <text class="login-subtitle">查看预订记录，管理个人信息</text>
      <button class="login-btn {{isLogging ? 'loading' : ''}}" bindtap="login" disabled="{{isLogging}}">
        <text wx:if="{{!isLogging}}">微信一键登录</text>
        <text wx:else>登录中...</text>
      </button>
    </view>

    <!-- 已登录状态 -->
    <view class="user-info" wx:else>
      <image class="user-avatar" src="/images/男士.jpg" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName}}</text>
      </view>
    </view>
  </view>

  <!-- 我的预订 -->
  <view class="booking-section" wx:if="{{isLoggedIn}}">
    <view class="section-header">
      <text class="section-title">我的预订</text>
      <text class="view-all" bindtap="viewAllBookings">查看全部</text>
    </view>



    <!-- 订单列表 -->
    <view class="booking-list" wx:if="{{recentBookings.length > 0}}">
        <view class="booking-item"
              wx:for="{{recentBookings}}"
              wx:key="orderNo"
              bindtap="onOrderItemTap"
              data-order="{{item}}">

          <view class="booking-header">
            <text class="booking-number">订单号：{{item.orderNo}}</text>
            <view class="booking-status {{item.status}}">
              <text class="status-text">{{item.statusText}}</text>
            </view>
          </view>
          
          <view class="booking-content">
            <view class="booking-info">
              <view class="booking-main-info">
                <text class="booking-room">{{item.roomName}}</text>
                <text class="booking-conference" wx:if="{{item.conferenceTitle}}">{{item.conferenceTitle}}</text>
              </view>
              <text class="booking-date">{{item.checkinDate}} - {{item.checkoutDate}} · {{item.nights}}晚</text>
            </view>
            <view class="booking-price">
              <text class="price-amount">¥{{item.totalPrice}}</text>
              <text class="price-deposit" wx:if="{{item.status === 'refunded' && item.refundAmount > 0}}">已退款¥{{item.refundAmount}}</text>
              <text class="price-deposit" wx:elif="{{item.depositAmount > 0}}">已付定金¥{{item.depositAmount}}</text>
            </view>
          </view>
          

        </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-bookings" wx:else>
      <text class="empty-icon">📋</text>
      <text class="empty-text">暂无预订记录</text>
      <text class="empty-subtitle">预订酒店后，订单信息将在这里显示</text>
    </view>
  </view>



  <!-- 帮助与反馈 -->
  <view class="help-section">
    <view class="section-title">帮助与反馈</view>
    <view class="help-grid">
      <view class="help-item" bindtap="showHelp">
        <text class="help-icon">❓</text>
        <text class="help-text">使用帮助</text>
      </view>
      <button class="help-item contact-service-btn" open-type="contact" bindcontact="onContactService">
        <text class="help-icon">💬</text>
        <text class="help-text">联系客服</text>
      </button>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">会议酒店预定 v1.0.0</text>
    <text class="copyright">© 2024 会议酒店预定平台</text>
  </view>
</view>
