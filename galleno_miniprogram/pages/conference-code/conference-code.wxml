<!--conference-code.wxml-->
<view class="container">
  <!-- 会议信息横幅 -->
  <view class="conference-banner">
    <view class="banner-icon">
      <text class="icon-key">🔑</text>
    </view>
    <view class="conference-info">
      <text class="conference-title">{{conferenceTitle}}</text>
      <text class="conference-location">{{conferenceLocation}}</text>
      <text class="conference-date">{{conferenceDate}}</text>
    </view>
  </view>

  <!-- 识别码输入区域 -->
  <view class="input-section">
    <view class="input-header">
      <text class="input-title">请输入会议识别码</text>
      <text class="input-subtitle">请输入主办方提供的会议识别码以继续预订酒店</text>
    </view>

    <!-- 识别码输入框 -->
    <view class="input-container">
      <text class="input-label">会议识别码</text>
      <view class="input-wrapper {{inputFocused ? 'focused' : ''}} {{hasError ? 'error' : ''}}" bindtap="focusInput">
        <input
          class="code-input"
          type="text"
          placeholder="请输入识别码"
          maxlength="12"
          value="{{inputCode}}"
          cursor-spacing="10"
          confirm-type="done"
          adjust-position="{{true}}"
          hold-keyboard="{{false}}"
          selection-start="-1"
          selection-end="-1"
          bindinput="onInputChange"
          bindfocus="onInputFocus"
          bindblur="onInputBlur"
          bindconfirm="verifyCode"
        />
        <text class="input-icon">🔒</text>
      </view>
      
      <view class="input-hint">
        <text class="hint-icon">ℹ️</text>
        <text class="hint-text">识别码为6-12位字符，区分大小写</text>
      </view>
      
      <!-- 错误信息 -->
      <view class="error-message" wx:if="{{errorMessage}}">
        <text class="error-icon">⚠️</text>
        <text class="error-text">{{errorMessage}}</text>
      </view>
    </view>

    <!-- 验证按钮 -->
    <button 
      class="verify-btn {{!canVerify ? 'disabled' : ''}} {{isVerifying ? 'loading' : ''}}" 
      disabled="{{!canVerify || isVerifying}}"
      bindtap="verifyCode"
    >
      <text wx:if="{{!isVerifying}}">验证识别码</text>
      <text wx:else>验证中...</text>
    </button>


  </view>

  <!-- 帮助信息 -->
  <view class="help-section">
    <view class="help-header">
      <text class="help-icon">❓</text>
      <text class="help-title">常见问题</text>
    </view>
    
    <view class="help-list">
      <view class="help-item">
        <text class="help-question">如何获取识别码？</text>
        <text class="help-answer">识别码由会议主办方提供，请查看会议邀请函或联系会议组织者。</text>
      </view>
      
      <view class="help-item">
        <text class="help-question">忘记识别码怎么办？</text>
        <text class="help-answer">请点击下方"联系客服"按钮，联系在线客服获取帮助。</text>
      </view>
      
      <view class="help-item">
        <text class="help-question">识别码输入错误？</text>
        <text class="help-answer">请检查大小写和字符是否正确，确保输入的识别码与主办方提供的完全一致。</text>
      </view>
    </view>
  </view>

  <!-- 联系客服 -->
  <view class="contact-section">
    <view class="contact-content">
      <view class="contact-info">
        <text class="contact-title">需要帮助？</text>
        <text class="contact-subtitle">联系会议客服获取支持</text>
      </view>
      <button class="contact-btn" open-type="contact" bindcontact="onContactService">
        <text class="contact-icon">💬</text>
        <text>联系客服</text>
      </button>
    </view>
  </view>
</view>
