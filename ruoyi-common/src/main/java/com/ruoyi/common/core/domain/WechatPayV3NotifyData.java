package com.ruoyi.common.core.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 微信支付V3版本回调通知数据结构
 * 用于解析微信V3回调的外层结构，包含加密的resource字段
 * 
 * <AUTHOR>
 */
public class WechatPayV3NotifyData
{
    /** 通知ID */
    private String id;

    /** 通知创建时间 */
    @JsonProperty("create_time")
    private String createTime;

    /** 通知类型 */
    @JsonProperty("resource_type")
    private String resourceType;

    /** 事件类型 */
    @JsonProperty("event_type")
    private String eventType;

    /** 回调摘要 */
    private String summary;

    /** 通知资源数据 */
    private Resource resource;

    /**
     * 通知资源数据（加密）
     */
    public static class Resource {
        /** 原始类型 */
        @JsonProperty("original_type")
        private String originalType;

        /** 加密算法 */
        private String algorithm;

        /** 密文 */
        private String ciphertext;

        /** 附加数据 */
        @JsonProperty("associated_data")
        private String associatedData;

        /** 随机串 */
        private String nonce;

        public String getOriginalType() {
            return originalType;
        }

        public void setOriginalType(String originalType) {
            this.originalType = originalType;
        }

        public String getAlgorithm() {
            return algorithm;
        }

        public void setAlgorithm(String algorithm) {
            this.algorithm = algorithm;
        }

        public String getCiphertext() {
            return ciphertext;
        }

        public void setCiphertext(String ciphertext) {
            this.ciphertext = ciphertext;
        }

        public String getAssociatedData() {
            return associatedData;
        }

        public void setAssociatedData(String associatedData) {
            this.associatedData = associatedData;
        }

        public String getNonce() {
            return nonce;
        }

        public void setNonce(String nonce) {
            this.nonce = nonce;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Resource getResource() {
        return resource;
    }

    public void setResource(Resource resource) {
        this.resource = resource;
    }

    /**
     * 判断是否为支付成功事件
     */
    public boolean isPaymentSuccess() {
        return "TRANSACTION.SUCCESS".equals(eventType);
    }

    /**
     * 判断是否为退款成功事件
     */
    public boolean isRefundSuccess() {
        return "REFUND.SUCCESS".equals(eventType);
    }
}
