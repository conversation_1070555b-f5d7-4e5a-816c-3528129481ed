<!--order-list.wxml-->
<view class="container">
  <!-- 标签栏 -->
  <view class="tab-bar">
    <scroll-view class="tab-scroll" scroll-x="true">
      <view class="tab-list">
        <view class="tab-item {{activeTab === item.key ? 'active' : ''}}"
              wx:for="{{tabs}}"
              wx:key="key"
              bindtap="onTabTap"
              data-tab="{{item.key}}">
          <text class="tab-name">{{item.name}}</text>
          <text class="tab-count" wx:if="{{item.count > 0}}">({{item.count}})</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon">⏳</view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 订单列表 -->
  <view class="order-list" wx:elif="{{!isEmpty}}">
    <view class="order-item"
          wx:for="{{filteredOrderList}}"
          wx:key="orderNo"
          bindtap="onOrderItemTap"
          data-order="{{item}}">
      
      <!-- 订单头部 -->
      <view class="order-header">
        <text class="order-number">订单号：{{item.orderNo}}</text>
        <view class="order-status {{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>

      <!-- 订单内容 -->
      <view class="order-content">
        <view class="order-info">
          <view class="info-row">
            <text class="info-label">会议</text>
            <text class="info-value">{{item.conferenceTitle || '会议酒店预订'}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">房型</text>
            <text class="info-value">{{item.roomName}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">入住</text>
            <text class="info-value">{{item.checkinDate}} ({{item.nights}}晚)</text>
          </view>
          <view class="info-row">
            <text class="info-label">退房</text>
            <text class="info-value">{{item.checkoutDate}}</text>
          </view>
        </view>
      </view>

      <!-- 订单底部 -->
      <view class="order-footer">
        <view class="price-info">
          <text class="price-label">
            {{item.status === 'refunded' ? '退款金额' : '订单金额'}}
          </text>
          <text class="price-value">
            ¥{{item.status === 'refunded' ? item.refundAmount : (item.totalPrice || item.depositAmount)}}
          </text>
        </view>
        
        <!-- 操作按钮 -->
        <view class="action-buttons">
          <view class="action-btn primary" wx:if="{{item.status === 'pending'}}">
            <text>立即支付</text>
          </view>
          <view class="action-btn" wx:else>
            <text>查看详情</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:else>
    <view class="empty-icon">📋</view>
    <text class="empty-title">{{emptyTitle}}</text>
    <text class="empty-subtitle">{{emptySubtitle}}</text>
    <button class="empty-btn" wx:if="{{activeTab === 'all'}}" bindtap="goToBooking">
      立即预订
    </button>
  </view>
</view>
