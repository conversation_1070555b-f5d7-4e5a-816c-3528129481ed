# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: **********************************************************************************************************************************************
                username: root
                password: mysql_TWc7Rp
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true


# 微信小程序配置
wechat:
  miniapp:
    # 小程序AppID
    appId: wx80ecd969d8dc4f6d
    # 小程序AppSecret
    appSecret: 780d9b814535e16b2b67fbd7f75fb9bf
    # access_token缓存时间（秒），默认7200秒（2小时）
    tokenCacheTime: 7200
  pay:
    # 商户号
    mchId: 1724178853
    # APIv3密钥
    apiV3Key: 6111ff40ca2a4d859621e284bc074bac
    # 商户私钥文件路径
    privateKeyPath: /app/apiclient_key.pem
    # 商户证书序列号
    merchantSerialNumber: 1B0CE287AD107AB325429D20A2EE26C94E9B69F5
    # 支付回调通知URL
    notifyUrl: https://galleno.potatocat.cn/api/wechat/pay/notify
    # 支付超时时间（分钟），默认30分钟
    timeoutMinutes: 30
    # 微信平台公钥文件路径（二选一）
    wechatPlatformPublicKeyPath: /app/pub_key.pem
    # 微信平台公钥字符串（二选一，PEM格式）
    # wechatPlatformPublicKey: |
    #   -----BEGIN PUBLIC KEY-----
    #   MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
    #   -----END PUBLIC KEY-----

