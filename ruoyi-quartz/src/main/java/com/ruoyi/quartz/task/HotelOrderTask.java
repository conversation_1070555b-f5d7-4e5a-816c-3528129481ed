package com.ruoyi.quartz.task;

import com.ruoyi.system.service.IHotelOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 酒店订单定时任务
 * 
 * <AUTHOR>
 */
@Component("hotelOrderTask")
public class HotelOrderTask
{
    private static final Logger log = LoggerFactory.getLogger(HotelOrderTask.class);

    @Autowired
    private IHotelOrderService hotelOrderService;

    /**
     * 处理超时订单
     * 扫描超过30分钟还是待支付的订单，将状态修改为已取消
     */
    public void handleTimeoutOrders()
    {
        log.info("开始执行超时订单处理任务");
        try
        {
            int timeoutMinutes = 30; // 30分钟超时
            int handledCount = hotelOrderService.handleTimeoutOrders(timeoutMinutes);
            if (handledCount > 0) {
                log.info("处理超时订单数量: {}", handledCount);
            } else {
                log.debug("没有发现超时订单");
            }
            log.info("超时订单处理任务执行完成");
        }
        catch (Exception e)
        {
            log.error("超时订单处理任务执行异常", e);
        }
    }

    /**
     * 处理超时订单（带参数）
     * 
     * @param params 参数，格式：超时分钟数，如"30"
     */
    public void handleTimeoutOrders(String params)
    {
        log.info("开始执行超时订单处理任务，参数: {}", params);
        try
        {
            int timeoutMinutes = 30; // 默认30分钟
            if (params != null && !params.trim().isEmpty()) {
                try {
                    timeoutMinutes = Integer.parseInt(params.trim());
                } catch (NumberFormatException e) {
                    log.warn("参数格式错误，使用默认值30分钟，参数: {}", params);
                }
            }
            
            int handledCount = hotelOrderService.handleTimeoutOrders(timeoutMinutes);
            if (handledCount > 0) {
                log.info("处理超时订单数量: {}，超时时间: {}分钟", handledCount, timeoutMinutes);
            } else {
                log.debug("没有发现超时订单，超时时间: {}分钟", timeoutMinutes);
            }
            log.info("超时订单处理任务执行完成");
        }
        catch (Exception e)
        {
            log.error("超时订单处理任务执行异常", e);
        }
    }
}
