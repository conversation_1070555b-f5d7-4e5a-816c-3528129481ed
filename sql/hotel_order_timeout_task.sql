-- 添加酒店订单超时处理定时任务
-- 执行时间：2025-01-17
-- 说明：添加定时任务，每5分钟扫描一次超过30分钟还是待支付的订单，将状态修改为已取消

-- 插入超时订单处理定时任务
INSERT INTO sys_job VALUES(
    4, 
    '超时订单处理', 
    'DEFAULT', 
    'hotelOrderTask.handleTimeoutOrders', 
    '0 */5 * * * ?', 
    '3', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    '', 
    null, 
    '每5分钟扫描超过30分钟还是待支付的订单，将状态修改为已取消'
);

-- 插入带参数的超时订单处理定时任务（备用）
INSERT INTO sys_job VALUES(
    5, 
    '超时订单处理（带参数）', 
    'DEFAULT', 
    'hotelOrderTask.handleTimeoutOrders(\'30\')', 
    '0 */5 * * * ?', 
    '3', 
    '1', 
    '1', 
    'admin', 
    sysdate(), 
    '', 
    null, 
    '每5分钟扫描超过指定分钟数还是待支付的订单，将状态修改为已取消，参数为超时分钟数'
);

-- 验证插入结果
SELECT job_id, job_name, job_group, invoke_target, cron_expression, status, remark 
FROM sys_job 
WHERE job_id IN (4, 5);
